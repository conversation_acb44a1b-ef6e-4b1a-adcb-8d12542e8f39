2025-07-08 19:06:43,707 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 19:06:43,707 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 19:06:45,073 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 19:06:45,864 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 19:06:45,865 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 19:06:45,865 - services.email_monitor - INFO - Processing email ID: 23
2025-07-08 19:06:46,226 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 19:06:46,228 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_190646_23.txt
2025-07-08 19:06:46,492 - services.email_monitor - INFO - Successfully processed email 23
2025-07-08 19:06:47,021 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:49:32,675 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 21:49:32,675 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 21:49:34,803 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 21:49:35,174 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 21:49:35,870 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:56:47,495 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 21:56:47,495 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 21:56:47,495 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 21:56:49,618 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 21:56:49,904 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 21:56:50,496 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:56:50,497 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 21:57:20,503 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 21:57:20,506 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 21:57:21,920 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 21:57:22,201 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 21:57:22,772 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:57:22,772 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 21:57:52,777 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 21:57:52,779 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 21:57:54,140 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 21:57:54,875 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 21:57:54,875 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 21:57:54,875 - services.email_monitor - INFO - Processing email ID: 24
2025-07-08 21:57:55,200 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 21:57:55,204 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_215755_24.txt
2025-07-08 21:57:55,465 - services.email_monitor - INFO - Successfully processed email 24
2025-07-08 21:57:55,983 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 21:57:55,984 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 22:00:52,296 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 22:00:52,296 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 22:00:52,296 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 22:00:54,402 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 22:00:55,301 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 22:00:55,301 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 22:00:55,301 - services.email_monitor - INFO - Processing email ID: 25
2025-07-08 22:00:55,726 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 22:00:55,727 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_220055_25.txt
2025-07-08 22:00:56,087 - services.email_monitor - INFO - Successfully processed email 25
2025-07-08 22:00:56,781 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 22:00:56,781 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 22:31:20,348 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 22:31:20,349 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 22:31:22,103 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 22:31:22,390 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 22:31:22,948 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:25:08,494 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 23:25:08,495 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:25:08,495 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:25:09,889 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:25:10,222 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 23:25:10,865 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:25:10,866 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 23:27:20,189 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 23:27:20,189 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:27:20,189 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:27:21,563 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:27:22,500 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:27:22,500 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 23:27:22,500 - services.email_monitor - INFO - Processing email ID: 25
2025-07-08 23:27:22,893 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:27:22,895 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_232722_25.txt
2025-07-08 23:27:22,896 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_232722_25.json
2025-07-08 23:27:22,896 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_232722_25.json
2025-07-08 23:27:22,896 - services.email_monitor - INFO - 🤖 Triggering automated agent workflow...
2025-07-08 23:27:22,897 - services.workflow_orchestrator - INFO - 🚀 Starting O2C workflow workflow_20250708_232722 for file: data/incoming_pos/PO_20250708_232722_25.txt
2025-07-08 23:27:22,897 - services.workflow_orchestrator - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:27:22,941 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:27:23,089 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:27:23,762 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:27:23,766 - root - ERROR - Error during short_term search: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:27:24,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:27:24,450 - root - ERROR - Error during entities search: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:27:24,487 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:27:25,131 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:27:29,803 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:27:29,803 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:27:29,804 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:27:29,806 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:27:29,836 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:27:30,175 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:28:12,776 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:28:12,778 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:12,779 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:12,782 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:13,390 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:13,392 - root - ERROR - Error during short_term save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:15,420 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:28:15,947 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:28:35,949 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:28:35,950 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:35,951 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:35,959 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:28:36,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:36,406 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:36,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:36,804 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:37,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:37,128 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:37,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:37,559 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:38,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:38,273 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:38,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:38,608 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:39,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:28:39,199 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:28:39,206 - services.workflow_orchestrator - INFO - Agent returned JSON directly (no file saved)
2025-07-08 23:28:39,206 - services.workflow_orchestrator - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:28:39,206 - services.email_monitor - ERROR - ❌ Automated workflow failed: ['No parsed file generated']
2025-07-08 23:28:39,536 - services.email_monitor - INFO - Successfully processed email 25
2025-07-08 23:28:40,143 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:28:40,144 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 23:29:10,149 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:29:10,151 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:29:11,610 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:29:11,899 - services.email_monitor - INFO - Found 0 potential PO emails
2025-07-08 23:29:12,444 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:29:12,444 - services.email_monitor - INFO - Waiting 30 seconds before next check...
2025-07-08 23:35:22,730 - services.email_monitor - INFO - Starting continuous email monitoring...
2025-07-08 23:35:22,730 - services.email_monitor - INFO - Starting email monitoring cycle...
2025-07-08 23:35:22,730 - services.email_monitor - INFO - Connecting to Gmail IMAP server...
2025-07-08 23:35:24,505 - services.email_monitor - INFO - Successfully connected to Gmail
2025-07-08 23:35:25,483 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:35:25,484 - services.email_monitor - INFO - Found 1 potential PO emails
2025-07-08 23:35:25,484 - services.email_monitor - INFO - Processing email ID: 25
2025-07-08 23:35:25,829 - services.email_monitor - INFO - Extracted text attachment: sample_po_for_testing.txt
2025-07-08 23:35:25,830 - services.email_monitor - INFO - Extracted PO content to: data/incoming_pos/PO_20250708_233525_25.txt
2025-07-08 23:35:25,830 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_233525_25.json
2025-07-08 23:35:25,830 - services.email_monitor - INFO - Saved email metadata to: data/processed_emails/EMAIL_20250708_233525_25.json
2025-07-08 23:35:25,831 - services.email_monitor - INFO - 🤖 Triggering automated agent workflow...
2025-07-08 23:35:25,831 - services.workflow_orchestrator - INFO - 🚀 Starting O2C workflow workflow_20250708_233525 for file: data/incoming_pos/PO_20250708_233525_25.txt
2025-07-08 23:35:25,831 - services.workflow_orchestrator - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:35:25,853 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:35:26,204 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:35:30,583 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:35:30,584 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:35:30,584 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:35:30,587 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:35:30,680 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:35:30,992 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:36:04,560 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:36:04,563 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:36:04,563 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:36:04,566 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:36:04,585 - services.workflow_orchestrator - INFO - Agent returned JSON directly (no file saved)
2025-07-08 23:36:04,585 - services.workflow_orchestrator - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:36:04,585 - services.email_monitor - ERROR - ❌ Automated workflow failed: ['No parsed file generated']
2025-07-08 23:36:04,871 - services.email_monitor - INFO - Successfully processed email 25
2025-07-08 23:36:06,122 - services.email_monitor - INFO - Disconnected from Gmail
2025-07-08 23:36:06,122 - services.email_monitor - INFO - Waiting 30 seconds before next check...
