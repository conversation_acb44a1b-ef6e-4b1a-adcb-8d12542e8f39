{"email_processing": {"email_id": "25", "processed_at": "2025-07-09T10:38:53.203109", "sender": "366 <PERSON><PERSON><PERSON> <<EMAIL>>", "subject": "PO", "date": "<PERSON><PERSON>, 8 Jul 2025 21:59:06 +0530", "attachments": ["sample_po_for_testing.txt"], "has_text_attachments": true, "text_attachment_count": 1}, "po_extraction": {"po_file_created": "PO_20250709_103853_25.txt", "po_file_path": "data/incoming_pos/PO_20250709_103853_25.txt", "extraction_status": "success", "workflow_stage": "extracted"}, "workflow_tracking": {"stage": "email_processed", "next_stage": "awaiting_agent_processing", "created_at": "2025-07-09T10:38:53.203116"}}