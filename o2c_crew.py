#!/usr/bin/env python3
"""
O2C CrewAI Orchestration
Main crew for Order-to-Cash processing with PO Parser Agent
"""

import os
import sys
import json
from datetime import datetime
from crewai import Agent, Task, Crew, Process
from agents.llm.deepseek_llm import create_deepseek_llm_for_parsing
from agents.tools.po_parsing_tools import get_po_parsing_tools
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class O2CCrew:
    """O2C CrewAI orchestration with PO Parser Agent"""
    
    def __init__(self, deepseek_api_key: str = None):
        """Initialize the O2C Crew"""
        self.deepseek_api_key = deepseek_api_key or os.getenv("DEEPSEEK_API_KEY")

        if not self.deepseek_api_key:
            raise ValueError("DeepSeek API key is required. Set DEEPSEEK_API_KEY environment variable.")

        # Set API key in environment for CrewAI
        os.environ["DEEPSEEK_API_KEY"] = self.deepseek_api_key

        self.llm = create_deepseek_llm_for_parsing()
        self.tools = get_po_parsing_tools()

        # Initialize agents
        self.po_parser_agent = self._create_po_parser_agent()

        # Create crew
        self.crew = self._create_crew()
    
    def _create_po_parser_agent(self) -> Agent:
        """Create the PO Parser Agent"""
        return Agent(
            role="Senior Purchase Order Parser",
            goal="Extract complete and accurate structured data from purchase order documents",
            backstory="""You are a highly experienced purchase order processing specialist with over 10 years 
            of experience in parsing various PO formats. You have worked with companies across different industries 
            and understand the nuances of different PO structures, terminology, and business requirements.
            
            Your expertise includes:
            - Identifying and extracting PO numbers, dates, and reference information
            - Parsing vendor and customer details with complete contact information
            - Accurately extracting line items with product codes, descriptions, quantities, and pricing
            - Calculating and validating pricing summaries including taxes and shipping
            - Understanding delivery requirements and special instructions
            - Interpreting payment terms and business conditions
            
            You are meticulous in your work and ensure that no critical information is missed. You understand 
            that accurate PO parsing is crucial for the entire order-to-cash process and downstream operations.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm,
            tools=self.tools,
            max_iter=3,
            memory=True
        )
    
    def _create_crew(self) -> Crew:
        """Create the CrewAI crew"""
        return Crew(
            agents=[self.po_parser_agent],
            tasks=[],  # Tasks will be added dynamically
            process=Process.sequential,
            verbose=True,
            memory=True
        )
    
    def create_po_parsing_task(self, po_filename: str = None, auto_select: bool = True) -> Task:
        """Create a comprehensive PO parsing task"""
        
        if po_filename:
            task_description = f"""
            Parse the purchase order file '{po_filename}' and extract all information into structured JSON format.
            
            STEP 1: Read the PO file
            - Use po_file_reader tool to read '{po_filename}'
            - Analyze the complete content including email metadata, body, and text attachments
            
            STEP 2: Extract structured data
            Parse and extract the following information with high accuracy:
            
            A) Basic PO Information:
               - PO number (look for patterns like PO-XXX, P.O. XXX, Purchase Order Number)
               - PO date (convert to YYYY-MM-DD format)
               - Any reference numbers or order IDs
            
            B) Vendor Information:
               - Company name
               - Complete address (street, city, state, zip, country)
               - Phone number
               - Email address
               - Contact person name and title
            
            C) Customer/Buyer Information:
               - Company name
               - Billing address
               - Shipping address (if different from billing)
               - Phone number
               - Email address
               - Contact person and department
            
            D) Line Items (for each product/service):
               - Item/product code or SKU
               - Detailed description
               - Quantity ordered
               - Unit of measure (pieces, units, etc.)
               - Unit price
               - Line total (quantity × unit price)
            
            E) Pricing Summary:
               - Subtotal (sum of all line totals)
               - Tax amount and rate
               - Shipping/handling charges
               - Any discounts applied
               - Final total amount
            
            F) Delivery Information:
               - Delivery address
               - Requested delivery date
               - Shipping method
               - Special delivery instructions
            
            G) Terms and Conditions:
               - Payment terms (Net 30, etc.)
               - Special conditions or requirements
               - Authorized person and signature
            
            STEP 3: Structure the data in this exact JSON format:
            {{
                "po_number": "string",
                "date": "YYYY-MM-DD",
                "reference_numbers": ["string"],
                "vendor": {{
                    "name": "string",
                    "address": {{
                        "street": "string",
                        "city": "string", 
                        "state": "string",
                        "zip": "string",
                        "country": "string"
                    }},
                    "contact": {{
                        "phone": "string",
                        "email": "string",
                        "person": "string",
                        "title": "string"
                    }}
                }},
                "customer": {{
                    "name": "string",
                    "billing_address": {{
                        "street": "string",
                        "city": "string",
                        "state": "string", 
                        "zip": "string",
                        "country": "string"
                    }},
                    "shipping_address": {{
                        "street": "string",
                        "city": "string",
                        "state": "string",
                        "zip": "string", 
                        "country": "string"
                    }},
                    "contact": {{
                        "phone": "string",
                        "email": "string",
                        "person": "string",
                        "department": "string"
                    }}
                }},
                "line_items": [
                    {{
                        "item_code": "string",
                        "description": "string",
                        "quantity": number,
                        "unit_of_measure": "string",
                        "unit_price": number,
                        "line_total": number
                    }}
                ],
                "pricing": {{
                    "subtotal": number,
                    "tax_rate": number,
                    "tax_amount": number,
                    "shipping": number,
                    "discount": number,
                    "total": number
                }},
                "delivery": {{
                    "address": "string",
                    "requested_date": "YYYY-MM-DD",
                    "shipping_method": "string",
                    "special_instructions": "string"
                }},
                "terms": {{
                    "payment_terms": "string",
                    "conditions": "string",
                    "authorized_by": "string"
                }}
            }}
            
            STEP 4: Validate and save the structured data
            - Ensure all required fields are present and accurate
            - Verify numerical calculations are correct
            - REQUIRED: Use po_data_saver tool to save the JSON data with original filename
            - This will automatically move the processed file to the archive folder
            - Optionally use po_validator tool for additional validation if needed
            
            IMPORTANT NOTES:
            - Extract information from both email body AND text attachments
            - If information appears in multiple places, use the most complete version
            - Convert all dates to YYYY-MM-DD format
            - Ensure all numerical values are properly typed (not strings)
            - If a field is not found, use empty string or null appropriately
            - Pay special attention to line item calculations and totals
            """
        else:
            task_description = """
            Identify and parse the most recent PO file from the incoming folder.
            
            STEP 1: List available files
            - Use po_list tool to see all available PO files
            - Identify the most recent file based on timestamp
            
            STEP 2: Parse the selected file
            - Follow the complete parsing process as described above
            - Extract all structured data with high accuracy
            
            STEP 3: Validate and save results
            - Validate the extracted data
            - REQUIRED: Use po_data_saver tool to save as structured JSON file
            - This will automatically move the processed file to archive
            """
        
        return Task(
            description=task_description,
            agent=self.po_parser_agent,
            expected_output="""Complete structured JSON data containing all PO information including:
            - PO number and date
            - Complete vendor and customer information
            - All line items with accurate pricing
            - Pricing summary with correct calculations
            - Delivery and terms information
            - Validation confirmation
            - Saved file location"""
        )
    
    def process_po_file(self, po_filename: str = None) -> dict:
        """Process a single PO file"""
        try:
            # Create parsing task
            task = self.create_po_parsing_task(po_filename)
            
            # Update crew with the task
            self.crew.tasks = [task]
            
            # Execute the crew
            result = self.crew.kickoff()
            
            return {
                "status": "success",
                "result": str(result),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def process_all_pending_pos(self) -> dict:
        """Process all pending PO files"""
        try:
            # Get list of files
            list_tool = self.tools[3]  # POListTool
            files_result = list_tool._run()
            
            if "No PO files found" in files_result:
                return {
                    "status": "success",
                    "message": "No pending PO files to process",
                    "processed_files": [],
                    "timestamp": datetime.now().isoformat()
                }
            
            # Extract filenames
            lines = files_result.split('\n')
            filenames = []
            for line in lines:
                if line.startswith('- ') and '.txt' in line:
                    filename = line.split(' ')[1]
                    filenames.append(filename)
            
            # Process each file
            results = []
            for filename in filenames:
                print(f"\n=== Processing {filename} ===")
                result = self.process_po_file(filename)
                results.append({
                    "filename": filename,
                    "result": result
                })
            
            return {
                "status": "success",
                "processed_files": results,
                "total_files": len(filenames),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


def main():
    """Main function for running the O2C Crew"""
    print("=== O2C CrewAI System ===\n")
    
    # Check for DeepSeek API key
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ Error: DEEPSEEK_API_KEY environment variable not set")
        print("\nPlease set your DeepSeek API key:")
        print("export DEEPSEEK_API_KEY='your-api-key-here'")
        print("\nOr set it in your shell profile for persistence.")
        return
    
    try:
        # Create O2C Crew
        print("🚀 Initializing O2C Crew with DeepSeek LLM...")
        crew = O2CCrew(deepseek_api_key=api_key)
        print("✅ O2C Crew initialized successfully!\n")
        
        # Process based on command line arguments
        if len(sys.argv) > 1:
            if sys.argv[1] == "--all":
                print("📋 Processing all pending PO files...")
                result = crew.process_all_pending_pos()
            else:
                filename = sys.argv[1]
                print(f"📄 Processing specific file: {filename}")
                result = crew.process_po_file(filename)
        else:
            print("📄 Processing most recent PO file...")
            result = crew.process_po_file()
        
        # Display results
        print("\n" + "="*50)
        print("🎯 PROCESSING RESULTS")
        print("="*50)
        print(json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    main()
